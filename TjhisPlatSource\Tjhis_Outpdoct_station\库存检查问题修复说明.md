# 库存检查问题修复说明

## 问题描述
- **现象**: 程序不重启时可以一直开药，重启后会正确提示库存不足
- **错误信息**: "药品【砂仁】库存不足，只能开6克\n预扣库存10克！"
- **CHECK_MODE设置**: 参数值为2，应该校验待发药和未缴费订单

## 问题根源
CHECK_MODE参数的缓存机制存在问题：
1. `SystemParm.GetParameterValue` 可能有内部缓存
2. 程序运行时参数值被缓存，修改参数后缓存未及时刷新
3. 重启程序后缓存被清空，重新读取了正确的参数值

## 修复方案

### 1. 增强参数缓存管理 (OutpdoctParameter.cs)
- 添加了参数变化监控日志
- 增加了异常处理机制
- 新增 `RefreshCheckModel()` 方法强制刷新缓存

### 2. 详细库存检查日志 (CDrugPresc.cs)
- 记录每次库存检查的详细信息
- 包含物理库存、未缴费量、待发药量等关键数据
- 库存不足时记录详细的缺口信息

### 3. 程序启动时参数刷新 (FrmOutpDoctorWorkStation.cs)
- 程序启动时自动刷新参数缓存
- 记录启动日志便于问题追踪

### 4. 参数测试工具 (FrmParameterTest.cs)
- 提供手动测试参数获取和缓存刷新的界面
- 便于运维人员验证参数状态

## 日志文件说明

### 日志位置
所有日志文件保存在: `..\Client\LOG\exLOG\`

### 日志文件类型
1. **门诊医生站_参数监控_YYYYMMDD.log** - 参数变化监控
2. **门诊医生站_参数异常_YYYYMMDD.log** - 参数获取异常
3. **门诊医生站_库存检查_YYYYMMDD.log** - 库存检查详情
4. **门诊医生站_库存不足_YYYYMMDD.log** - 库存不足警告
5. **门诊医生站_启动日志_YYYYMMDD.log** - 程序启动记录
6. **门诊医生站_参数测试_YYYYMMDD.log** - 参数测试记录

### 日志格式
```
[YYYY-MM-DD HH:mm:ss] [级别] [模块] 消息内容
```

## 使用方法

### 1. 验证修复效果
1. 编译并运行程序
2. 修改CHECK_MODE参数值
3. 观察日志文件中的参数变化记录
4. 测试库存检查是否立即生效

### 2. 手动刷新参数缓存
如果发现参数未及时生效，可以：
1. 打开参数测试工具 (FrmParameterTest)
2. 点击"刷新参数缓存"按钮
3. 验证参数值是否更新

### 3. 问题排查
1. 查看参数监控日志，确认参数值变化
2. 查看库存检查日志，分析库存计算过程
3. 查看异常日志，排查系统错误

## 测试建议

### 测试场景1: 参数缓存刷新
1. 启动程序，记录当前CHECK_MODE值
2. 在数据库中修改CHECK_MODE参数
3. 不重启程序，测试库存检查是否使用新参数值
4. 查看参数监控日志验证

### 测试场景2: 库存计算准确性
1. 选择一个库存较少的药品（如砂仁）
2. 查看库存检查日志中的详细计算过程
3. 验证物理库存、未缴费量、待发药量的计算是否正确
4. 确认可用库存 = 物理库存 - 未缴费量 - 待发药量

### 测试场景3: 异常处理
1. 模拟参数获取失败的情况
2. 验证程序是否使用默认值继续运行
3. 检查异常日志是否正确记录

## 注意事项
1. 日志文件按天分割，避免单个文件过大
2. 异常处理中忽略了日志写入错误，确保主业务不受影响
3. 参数刷新方法是线程安全的
4. 建议定期清理超过30天的旧日志文件

## 后续优化建议
1. 考虑添加参数自动刷新机制（如定时刷新）
2. 可以扩展到其他重要参数的缓存管理
3. 考虑使用配置文件控制日志级别和输出
