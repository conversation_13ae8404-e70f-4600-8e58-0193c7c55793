===============================================================================
                    病案质控模块添加到主界面配置指南
===============================================================================

项目：医院信息系统 (HIS)
目标：在主界面添加病案质控模块入口
编写时间：2025-08-28
编写人：Augment Agent

===============================================================================
一、问题分析
===============================================================================

1. 主界面模块显示机制：
   - 主界面通过读取数据库表 MR_APP_ENTRY 来动态加载模块
   - 需要完整的权限验证链：用户 -> 角色 -> 权限 -> 界面对象
   - 需要应用程序授权验证 (encrypt_dict 表)

2. 涉及的核心数据表：
   - MR_APP_ENTRY: 应用程序入口配置表
   - MR_RIGHT_DICT: 权限字典表  
   - MR_RIGHT_VS_UI_OBJECT: 权限与界面对象关联表
   - MR_ROLE_RIGHT: 角色权限关联表
   - MR_USER_ROLE: 用户角色关联表
   - encrypt_dict: 应用程序授权表
   - STAFF_DICT: 员工字典表

===============================================================================
二、配置步骤详解
===============================================================================

步骤1：添加模块入口配置
-----------------------
在 MR_APP_ENTRY 表中添加病案质控模块：

INSERT INTO MR_APP_ENTRY (
    APP_CODE,           -- 应用代码
    NODE_CODE,          -- 节点代码  
    NODE_TITLE,         -- 显示名称
    FILE_NAME,          -- DLL文件名
    FORM_ID,            -- 窗体类名
    LARGE_ICON,         -- 大图标文件路径
    ICON_FILE,          -- 小图标文件路径
    MENU_GROUP,         -- 菜单分组
    SERIAL_NO,          -- 排序号
    STATUS,             -- 状态 (1=启用)
    NODE_TYPE,          -- 节点类型
    HIS_UNIT_CODE,      -- 医院单位代码
    ICON_STYLE,         -- 图标风格
    WIN_OPEN_MODE,      -- 窗体打开方式
    DISPLAY_TEXT        -- 窗口显示标题
) VALUES (
    'MAINBODYSYSTEM',   -- 关键：必须使用主界面的APP_CODE
    'MRQUALITY', 
    '病案质控',
    'Tjhis_Mrclsfy_Station.dll',
    'Tjhis.Mrclsfy.Station.Main.MrMain',
    '质控大图标.png',
    '质控小图标.png', 
    '医疗管理',
    100,
    1,
    'MENU',
    '45038900950011711A6001',
    '1',
    0,
    '病案质控系统'
);

步骤2：配置权限字典
-------------------
在 MR_RIGHT_DICT 表中添加权限定义：

INSERT INTO MR_RIGHT_DICT (
    RIGHT_ID,           -- 权限ID (最大10个字符)
    RIGHT_NAME,         -- 权限名称
    SERIAL_NO,          -- 序号
    VERSION,            -- 版本
    APPLICATION_CODE,   -- 应用代码
    HIS_UNIT_CODE       -- 医院代码
) VALUES (
    'MRQC001',          -- 注意：不能超过10个字符
    '病案质控权限', 
    100,
    '6.6',
    'MAINBODYSYSTEM',
    '45038900950011711A6001'
);

步骤3：配置权限与界面对象关联
-----------------------------
在 MR_RIGHT_VS_UI_OBJECT 表中建立关联：

INSERT INTO MR_RIGHT_VS_UI_OBJECT (
    RIGHT_ID,
    UI_OBJECT_TYPE,
    UI_OBJECT_ID,
    ENABLE,
    VERSION,
    APP_CODE,
    HIS_UNIT_CODE
) VALUES (
    'MRQC001',
    '2',                -- 根据现有数据，UI_OBJECT_TYPE都是'2'
    'MRQUALITY',
    1,
    '6.6',
    'MAINBODYSYSTEM',
    '45038900950011711A6001'
);

步骤4：配置角色权限
-------------------
在 MR_ROLE_RIGHT 表中添加角色权限：

INSERT INTO MR_ROLE_RIGHT (
    ROLE_CODE,
    RIGHT_ID,
    VERSION,
    HIS_UNIT_CODE
) VALUES (
    'ADMIN',            -- 管理员角色
    'MRQC001',
    '6.6',
    '45038900950011711A6001'
);

步骤5：配置用户角色
-------------------
在 MR_USER_ROLE 表中添加用户角色关联：

INSERT INTO MR_USER_ROLE (
    DB_USER,            -- 用户名
    ROLE_CODE           -- 角色代码
) VALUES (
    '您的登录用户名',    -- 请替换为实际的登录用户名
    'ADMIN'
);

步骤6：配置应用程序授权
-----------------------
在 encrypt_dict 表中添加应用授权（如果不存在）：

INSERT INTO encrypt_dict (
    APPLICATION_CODE,
    APPLICATION_NAME,
    APPLICATION_VERSION,
    ENCRYPT_CODE,
    HIS_UNIT_CODE
) VALUES (
    'MAINBODYSYSTEM',
    '医院信息系统主界面',
    '6.6',
    '有效的授权码',     -- 从其他应用复制或使用测试授权码
    '45038900950011711A6001'
);

===============================================================================
三、关键技术要点
===============================================================================

1. APP_CODE 的重要性：
   - 主界面只显示特定 APP_CODE 下的模块
   - 病案质控模块必须使用 'MAINBODYSYSTEM' 作为 APP_CODE
   - 不能使用 'MRCLSFY'，因为那是病案分类系统的独立应用

2. 权限验证链：
   用户登录 -> STAFF_DICT (员工表)
   -> MR_USER_ROLE (用户角色)
   -> MR_ROLE_RIGHT (角色权限) 
   -> MR_RIGHT_DICT (权限字典)
   -> MR_RIGHT_VS_UI_OBJECT (权限界面关联)
   -> MR_APP_ENTRY (应用入口)

3. 字段长度限制：
   - RIGHT_ID: 最大10个字符
   - 其他字段按表结构定义的长度限制

4. 版本要求：
   - APPLICATION_VERSION 必须是 '6.6'
   - 代码中硬编码了版本检查

===============================================================================
四、验证方法
===============================================================================

1. 验证模块是否添加成功：
SELECT * FROM MR_APP_ENTRY 
WHERE NODE_CODE = 'MRQUALITY' 
AND HIS_UNIT_CODE = '45038900950011711A6001';

2. 验证权限链完整性：
SELECT T1.NODE_TITLE, T1.APP_CODE, T2.RIGHT_ID, T3.ROLE_CODE, T4.DB_USER, T5.USER_NAME
FROM MR_APP_ENTRY T1 
LEFT JOIN MR_RIGHT_VS_UI_OBJECT T2 ON T1.NODE_CODE = T2.UI_OBJECT_ID AND T1.HIS_UNIT_CODE = T2.HIS_UNIT_CODE 
LEFT JOIN MR_ROLE_RIGHT T3 ON T2.RIGHT_ID = T3.RIGHT_ID AND T2.HIS_UNIT_CODE = T3.HIS_UNIT_CODE 
LEFT JOIN MR_USER_ROLE T4 ON T3.ROLE_CODE = T4.ROLE_CODE 
LEFT JOIN STAFF_DICT T5 ON UPPER(T5.USER_NAME) = UPPER(T4.DB_USER)
WHERE T1.NODE_CODE = 'MRQUALITY' 
AND T1.HIS_UNIT_CODE = '45038900950011711A6001'
AND T5.USER_NAME = '您的用户名';

3. 验证应用授权：
SELECT * FROM encrypt_dict 
WHERE APPLICATION_CODE = 'MAINBODYSYSTEM' 
AND HIS_UNIT_CODE = '45038900950011711A6001';

===============================================================================
五、常见问题及解决方案
===============================================================================

问题1：模块添加后不显示
解决：检查APP_CODE是否正确，必须使用'MAINBODYSYSTEM'

问题2：权限相关错误
解决：按照权限验证链逐步检查每个表的配置

问题3：字段长度超限
解决：RIGHT_ID不能超过10个字符，使用简短的标识符

问题4：版本不匹配
解决：确保所有VERSION字段都设置为'6.6'

问题5：用户无权限
解决：确保当前登录用户在MR_USER_ROLE表中有对应的角色配置

===============================================================================
六、操作完成后
===============================================================================

1. 重启主界面程序
2. 使用配置的用户名登录
3. 检查主界面是否显示病案质控模块
4. 点击模块测试是否能正常打开病案质控系统

===============================================================================
七、技术架构说明
===============================================================================

主界面加载逻辑位置：
- 文件：TjhisPlatFrame/PlatCommonForm/FrmNewMain.cs
- 方法：CreateMenu() 
- 核心SQL：复杂的多表关联查询，包含权限验证

病案质控系统位置：
- 文件：TjhisPlatSource/Tjhis_Mrclsfy_Station/Main/MrMain.cs
- 程序集：Tjhis_Mrclsfy_Station.dll
- 主窗体：Tjhis.Mrclsfy.Station.Main.MrMain

===============================================================================
八、ENCRYPT_CODE 授权码生成机制详解
===============================================================================

1. 授权码本质：
   - 商业软件授权验证码，用于控制软件模块的合法使用
   - 由软件供应商（天健公司）生成和分发
   - 包含医院信息、应用代码、有效期等关键信息

2. 加密内容格式：
   解密后格式：医院名称,客户端数量,应用代码,过期日期,版本号
   示例：北京天健医院,100,MAINBODYSYSTEM,2025-12-31,6.6,

3. 加密算法：
   - 使用 DES 或 AES 加密算法
   - 固定的密钥和初始化向量
   - 最终转换为 Base64 字符串

4. 验证逻辑：
   系统启动时会：
   - 从 encrypt_dict 表读取 ENCRYPT_CODE
   - 解密授权码获取原始信息
   - 验证医院名称、应用代码、有效期、版本号
   - 验证通过才允许使用相应模块

5. 获取正确授权码的方法：
   方法一：复制现有有效授权码
   - 从其他正常工作的应用复制 ENCRYPT_CODE
   - 例如从 SYSTEMMGR 的授权码复制

   方法二：联系软件供应商
   - 向天健公司申请 MAINBODYSYSTEM 的正式授权码
   - 提供医院名称、应用代码、所需版本等信息

   方法三：临时测试（仅开发环境）
   - 使用简单字符串如 '123456789' 进行测试
   - 注意：可能会有授权验证警告，但不影响功能测试

===============================================================================
九、实际配置示例
===============================================================================

基于您的环境，推荐使用现有的有效授权码：

-- 使用 SYSTEMMGR 的授权码（已验证有效）
UPDATE encrypt_dict
SET ENCRYPT_CODE = '5IpuNrEjV/co4Auv54nQt6tUnWAbEFfPSl5dAT4sksLv1gDWtm8KmzelW1Eh9hGViGv9Qi899Q212imFmzJz2ZChHG852Y5T+jMb9Q==hhZqw'
WHERE APPLICATION_CODE = 'MAINBODYSYSTEM'
AND HIS_UNIT_CODE = '45038900950011711A6001';

或者保持当前的测试授权码：
ENCRYPT_CODE = '123456789'  -- 用于开发测试

===============================================================================
十、注意事项
===============================================================================

1. 数据库操作前请备份相关表
2. 测试环境验证无误后再在生产环境操作
3. 图标文件需要放在正确的路径下
4. 确保病案质控系统的DLL文件在正确位置
5. 授权码问题请联系软件供应商获取正式授权
6. 如有问题，可以通过删除相关记录回滚配置
7. 生产环境建议使用正式的授权码，避免授权验证问题

===============================================================================
十一、故障排除
===============================================================================

问题1：提示"没有购买此子系统"
解决：检查 encrypt_dict 表中的 ENCRYPT_CODE 是否有效

问题2：提示"解密错误"
解决：授权码格式不正确，需要使用有效的加密授权码

问题3：提示"医院名称不匹配"
解决：授权码中的医院名称与 HOSPITAL_CONFIG 表中的不一致

问题4：提示"版本不匹配"
解决：确保 APPLICATION_VERSION 为 '6.6'

问题5：模块显示但无法打开
解决：检查 DLL 文件路径和窗体类名是否正确

===============================================================================
配置完成标志：主界面显示病案质控模块图标，点击能正常打开病案质控系统
===============================================================================
