-- 砂仁库存问题诊断查询脚本
-- 创建时间：2025-08-28
-- 目的：诊断砂仁(63080238YP1)库存验证失败的原因

-- 1. 查询砂仁的基本信息
SELECT '=== 砂仁基本信息 ===' AS 查询类型;
SELECT 
    DRUG_CODE,
    DRUG_NAME,
    DRUG_SPEC,
    PACKAGE_SPEC,
    PACKAGE_UNITS,
    RETAIL_PRICE,
    DRUG_TYPE,
    ACTIVE_INDICATOR
FROM DRUG_DICT 
WHERE DRUG_CODE = '63080238YP1'
   OR DRUG_NAME LIKE '%砂仁%';

-- 2. 查询砂仁的库存信息
SELECT '=== 砂仁库存信息 ===' AS 查询类型;
SELECT 
    DRUG_CODE,
    DRUG_NAME,
    DRUG_SPEC,
    PACKAGE_SPEC,
    FIRM_ID,
    STORAGE,
    BATCH_NO,
    QUANTITY,
    SUPPLY_INDICATOR,
    EXPIRE_DATE,
    HIS_UNIT_CODE,
    LAST_UPDATETIME
FROM DRUG_STOCK 
WHERE DRUG_CODE = '63080238YP1'
   AND HIS_UNIT_CODE = '45038900950011711A6001'
ORDER BY STORAGE, EXPIRE_DATE, BATCH_NO;

-- 3. 模拟GetDrugStockForUpdate的完整查询（包含价格表关联）
SELECT '=== 完整查询（含价格表关联）===' AS 查询类型;
SELECT 
    S.DRUG_CODE,
    S.DRUG_NAME,
    S.TRADE_NAME,
    D.DOSE_PER_UNIT,
    D.DOSE_UNITS,
    0.0000000000 orders_dosage_in_stock_units,
    S.DRUG_SPEC,
    S.UNITS,
    S.FIRM_ID,
    S.PACKAGE_SPEC,
    S.PACKAGE_UNITS,
    S.BATCH_NO,
    S.QUANTITY,
    S.BATCH_CODE,
    S.EXPIRE_DATE,
    S.PURCHASE_PRICE,
    S.DISCOUNT,
    S.TRADE_PRICE,
    S.RETAIL_PRICE,
    S.SUPPLIER,
    S.SUB_STORAGE,
    S.LOCATION_CODE,
    (select max(amount_per_package) 
     from CURRENT_DRUG_MD_PRICE_LIST temp 
     where temp.drug_code = S.drug_code 
       and temp.min_spec = S.drug_spec 
       and temp.drug_spec = S.package_spec 
       and temp.firm_id = S.firm_id  
       and temp.HIS_UNIT_CODE = '45038900950011711A6001' 
       and temp.START_DATE <= sysdate 
       and (temp.STOP_DATE >= sysdate OR temp.STOP_DATE is null)) amount_per_package,
    S.STORAGE,
    S.PACKAGE_1,
    S.PACKAGE_SPEC_1,
    S.PACKAGE_UNITS_1,
    S.PACKAGE_2,
    S.PACKAGE_SPEC_2,
    S.PACKAGE_UNITS_2,
    S.SUPPLY_INDICATOR,
    S.DOCUMENT_NO,
    S.PURCHASE_PRICE_LAST,
    S.FROZEN_FLAG,
    S.QUANTITY_PRE,
    S.LAST_UPDATETIME,
    S.GUID 
FROM DRUG_STOCK S, DRUG_DICT D 
WHERE S.DRUG_CODE = D.DRUG_CODE 
  AND D.DRUG_SPEC = S.DRUG_SPEC 
  AND S.STORAGE = '110202'  -- 药房代码
  AND S.SUPPLY_INDICATOR = 1 
  AND S.QUANTITY > 0 
  AND S.drug_code = '63080238YP1'
  AND S.HIS_UNIT_CODE = '45038900950011711A6001'
  AND S.drug_spec = '1g'
  AND S.package_spec = '1g'
  AND S.firm_id = '重庆康嘉'
ORDER BY S.EXPIRE_DATE ASC, S.BATCH_NO ASC;

-- 4. 简化查询（不含价格表关联）
SELECT '=== 简化查询（不含价格表关联）===' AS 查询类型;
SELECT 
    S.DRUG_CODE,
    S.DRUG_NAME,
    S.QUANTITY,
    S.BATCH_NO,
    S.EXPIRE_DATE,
    S.FIRM_ID,
    S.DRUG_SPEC,
    S.PACKAGE_SPEC
FROM DRUG_STOCK S 
WHERE S.STORAGE = '110202'
  AND S.SUPPLY_INDICATOR = 1 
  AND S.QUANTITY > 0 
  AND S.drug_code = '63080238YP1'
  AND S.drug_spec = '1g'
  AND S.package_spec = '1g'
  AND S.firm_id = '重庆康嘉'
  AND S.HIS_UNIT_CODE = '45038900950011711A6001';

-- 5. 检查价格表中砂仁的记录
SELECT '=== 价格表中的砂仁记录 ===' AS 查询类型;
SELECT 
    drug_code,
    min_spec,
    drug_spec,
    firm_id,
    amount_per_package,
    START_DATE,
    STOP_DATE,
    HIS_UNIT_CODE
FROM CURRENT_DRUG_MD_PRICE_LIST 
WHERE drug_code = '63080238YP1'
  AND HIS_UNIT_CODE = '45038900950011711A6001'
  AND START_DATE <= sysdate 
  AND (STOP_DATE >= sysdate OR STOP_DATE is null);

-- 6. 检查DRUG_DICT表中砂仁的记录
SELECT '=== DRUG_DICT表中的砂仁记录 ===' AS 查询类型;
SELECT 
    DRUG_CODE,
    DRUG_NAME,
    DRUG_SPEC,
    ACTIVE_INDICATOR
FROM DRUG_DICT 
WHERE DRUG_CODE = '63080238YP1';

-- 7. 检查不同厂家的砂仁库存
SELECT '=== 不同厂家的砂仁库存 ===' AS 查询类型;
SELECT 
    FIRM_ID,
    SUM(QUANTITY) AS 总库存,
    COUNT(*) AS 批次数量
FROM DRUG_STOCK 
WHERE DRUG_CODE = '63080238YP1'
  AND STORAGE = '110202'
  AND SUPPLY_INDICATOR = 1
  AND HIS_UNIT_CODE = '45038900950011711A6001'
GROUP BY FIRM_ID;

-- 8. 检查是否存在DRUG_DICT和DRUG_STOCK的关联问题
SELECT '=== DRUG_DICT和DRUG_STOCK关联检查 ===' AS 查询类型;
SELECT 
    'DRUG_STOCK中存在但DRUG_DICT中不存在' AS 问题类型,
    S.DRUG_CODE,
    S.DRUG_SPEC
FROM DRUG_STOCK S
LEFT JOIN DRUG_DICT D ON S.DRUG_CODE = D.DRUG_CODE AND S.DRUG_SPEC = D.DRUG_SPEC
WHERE S.DRUG_CODE = '63080238YP1'
  AND S.HIS_UNIT_CODE = '45038900950011711A6001'
  AND D.DRUG_CODE IS NULL

UNION ALL

SELECT 
    'DRUG_DICT中存在但DRUG_STOCK中不存在' AS 问题类型,
    D.DRUG_CODE,
    D.DRUG_SPEC
FROM DRUG_DICT D
LEFT JOIN DRUG_STOCK S ON D.DRUG_CODE = S.DRUG_CODE AND D.DRUG_SPEC = S.DRUG_SPEC
WHERE D.DRUG_CODE = '63080238YP1'
  AND S.DRUG_CODE IS NULL;
