using System;
using System.Windows.Forms;
using Tjhis.Outpdoct.Station.Common;

namespace Tjhis.Outpdoct.Station.Views.SystemSetting
{
    /// <summary>
    /// 参数缓存测试工具
    /// 用于验证CHECK_MODE参数的缓存刷新机制
    /// </summary>
    public partial class FrmParameterTest : Form
    {
        public FrmParameterTest()
        {
            InitializeComponent();
        }

        private void InitializeComponent()
        {
            this.btnGetCheckMode = new System.Windows.Forms.Button();
            this.btnRefreshCache = new System.Windows.Forms.Button();
            this.txtResult = new System.Windows.Forms.TextBox();
            this.lblInfo = new System.Windows.Forms.Label();
            this.SuspendLayout();
            
            // 
            // lblInfo
            // 
            this.lblInfo.AutoSize = true;
            this.lblInfo.Location = new System.Drawing.Point(12, 9);
            this.lblInfo.Name = "lblInfo";
            this.lblInfo.Size = new System.Drawing.Size(400, 13);
            this.lblInfo.TabIndex = 0;
            this.lblInfo.Text = "参数缓存测试工具 - 用于验证CHECK_MODE参数的缓存刷新机制";
            
            // 
            // btnGetCheckMode
            // 
            this.btnGetCheckMode.Location = new System.Drawing.Point(12, 35);
            this.btnGetCheckMode.Name = "btnGetCheckMode";
            this.btnGetCheckMode.Size = new System.Drawing.Size(120, 30);
            this.btnGetCheckMode.TabIndex = 1;
            this.btnGetCheckMode.Text = "获取CHECK_MODE";
            this.btnGetCheckMode.UseVisualStyleBackColor = true;
            this.btnGetCheckMode.Click += new System.EventHandler(this.btnGetCheckMode_Click);
            
            // 
            // btnRefreshCache
            // 
            this.btnRefreshCache.Location = new System.Drawing.Point(150, 35);
            this.btnRefreshCache.Name = "btnRefreshCache";
            this.btnRefreshCache.Size = new System.Drawing.Size(120, 30);
            this.btnRefreshCache.TabIndex = 2;
            this.btnRefreshCache.Text = "刷新参数缓存";
            this.btnRefreshCache.UseVisualStyleBackColor = true;
            this.btnRefreshCache.Click += new System.EventHandler(this.btnRefreshCache_Click);
            
            // 
            // txtResult
            // 
            this.txtResult.Location = new System.Drawing.Point(12, 80);
            this.txtResult.Multiline = true;
            this.txtResult.Name = "txtResult";
            this.txtResult.ScrollBars = System.Windows.Forms.ScrollBars.Vertical;
            this.txtResult.Size = new System.Drawing.Size(560, 300);
            this.txtResult.TabIndex = 3;
            
            // 
            // FrmParameterTest
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(6F, 13F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(584, 392);
            this.Controls.Add(this.txtResult);
            this.Controls.Add(this.btnRefreshCache);
            this.Controls.Add(this.btnGetCheckMode);
            this.Controls.Add(this.lblInfo);
            this.Name = "FrmParameterTest";
            this.Text = "参数缓存测试工具";
            this.ResumeLayout(false);
            this.PerformLayout();
        }

        private System.Windows.Forms.Button btnGetCheckMode;
        private System.Windows.Forms.Button btnRefreshCache;
        private System.Windows.Forms.TextBox txtResult;
        private System.Windows.Forms.Label lblInfo;

        private void btnGetCheckMode_Click(object sender, EventArgs e)
        {
            try
            {
                string checkMode = PrescParameter.CheckModel;
                string result = string.Format(
                    "[{0:yyyy-MM-dd HH:mm:ss}] 当前CHECK_MODE值: {1}\r\n" +
                    "说明: 0=不校验, 1=仅校验待发药, 2=校验待发药和未缴费\r\n\r\n",
                    DateTime.Now, checkMode
                );
                
                txtResult.AppendText(result);
                txtResult.ScrollToCaret();
                
                // 同时记录到日志文件
                string logPath = @"..\Client\LOG\exLOG\门诊医生站_参数测试_" + DateTime.Now.ToString("yyyyMMdd") + ".log";
                System.IO.File.AppendAllText(logPath, result);
            }
            catch (Exception ex)
            {
                string error = string.Format(
                    "[{0:yyyy-MM-dd HH:mm:ss}] 获取CHECK_MODE失败: {1}\r\n\r\n",
                    DateTime.Now, ex.Message
                );
                txtResult.AppendText(error);
                txtResult.ScrollToCaret();
            }
        }

        private void btnRefreshCache_Click(object sender, EventArgs e)
        {
            try
            {
                PrescParameter.RefreshCheckModel();
                string result = string.Format(
                    "[{0:yyyy-MM-dd HH:mm:ss}] 参数缓存已强制刷新\r\n\r\n",
                    DateTime.Now
                );
                
                txtResult.AppendText(result);
                txtResult.ScrollToCaret();
                
                // 同时记录到日志文件
                string logPath = @"..\Client\LOG\exLOG\门诊医生站_参数测试_" + DateTime.Now.ToString("yyyyMMdd") + ".log";
                System.IO.File.AppendAllText(logPath, result);
                
                MessageBox.Show("参数缓存已刷新！", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            catch (Exception ex)
            {
                string error = string.Format(
                    "[{0:yyyy-MM-dd HH:mm:ss}] 刷新参数缓存失败: {1}\r\n\r\n",
                    DateTime.Now, ex.Message
                );
                txtResult.AppendText(error);
                txtResult.ScrollToCaret();
                
                MessageBox.Show("刷新失败: " + ex.Message, "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
    }
}
