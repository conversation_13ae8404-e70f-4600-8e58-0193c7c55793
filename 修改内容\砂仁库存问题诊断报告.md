# 砂仁库存问题诊断报告

## 问题现象
- 用户点击发药时提示："药品砂仁的库存数量不够!"
- 数据库中砂仁确实有库存：
  - 记录1：STORAGE=110202，QUANTITY=16.00
  - 记录3：STORAGE=110202，QUANTITY=1518.00

## 问题根源分析

### 1. 双重库存验证机制冲突
系统中存在两套库存验证逻辑：
- **新的统一库存验证器** (`UnifiedStockValidator.cs`) - 日志显示验证通过
- **旧的GetDrugStockForUpdate方法** - 实际触发错误提示的地方

### 2. 错误提示的具体位置
在 `FrmInHospitalMorePresdispDeliver.cs` 中：

**第541行**：
```csharp
XtraMessageBox.Show("药品" + prescdetadt.Rows[j]["DRUG_NAME"] + "的库存不够，处方号'" + masterdr["PRESC_NO"].ToString() + "'不能发药！", "提示");
```

**第548行**：
```csharp
XtraMessageBox.Show("药品" + prescdetadt.Rows[j]["DRUG_NAME"] + "的库存数量不够,处方号'" + masterdr["PRESC_NO"].ToString() + "'不能发药！", "提示");
```

### 3. GetDrugStockForUpdate方法的问题
该方法的SQL查询条件可能过于严格：

```sql
SELECT S.DRUG_CODE,S.DRUG_NAME,S.TRADE_NAME,D.DOSE_PER_UNIT,D.DOSE_UNITS,
0.0000000000 orders_dosage_in_stock_units,S.DRUG_SPEC,S.UNITS,S.FIRM_ID,S.PACKAGE_SPEC,
S.PACKAGE_UNITS,S.BATCH_NO,S.QUANTITY,S.BATCH_CODE,S.EXPIRE_DATE,S.PURCHASE_PRICE,
S.DISCOUNT,S.TRADE_PRICE,S.RETAIL_PRICE,S.SUPPLIER,S.SUB_STORAGE,S.LOCATION_CODE,
(select max(amount_per_package) 
from CURRENT_DRUG_MD_PRICE_LIST temp where temp.drug_code =S.drug_code and temp.min_spec=S.drug_spec 
and temp.drug_spec=S.package_spec and temp.firm_id = S.firm_id  and temp.HIS_UNIT_CODE='45038900950011711A6001' and temp.START_DATE <= sysdate 
and (temp.STOP_DATE >= sysdate OR temp.STOP_DATE is null ) ) amount_per_package,
S.STORAGE,S.PACKAGE_1,S.PACKAGE_SPEC_1,S.PACKAGE_UNITS_1,S.PACKAGE_2,S.PACKAGE_SPEC_2,
S.PACKAGE_UNITS_2,S.SUPPLY_INDICATOR,S.DOCUMENT_NO,S.PURCHASE_PRICE_LAST,S.FROZEN_FLAG,
S.QUANTITY_PRE,S.LAST_UPDATETIME , S.GUID 
FROM DRUG_STOCK S,DRUG_DICT D 
WHERE ( S.DRUG_CODE = D.DRUG_CODE ) and  ( D.DRUG_SPEC = S.DRUG_SPEC ) 
and  ( ( S.STORAGE = :as_storage_code ) AND  ( S.SUPPLY_INDICATOR = 1 ) )  
AND ( S.QUANTITY > 0 ) and S.drug_code =:as_drug_code 
and S.drug_spec =:as_drug_spec and S.package_spec =:as_package_spec 
and S.firm_id =:as_firm_id and S.HIS_UNIT_CODE='45038900950011711A6001'
```

## 可能的问题原因

### 1. 价格表关联问题
SQL中包含了对 `CURRENT_DRUG_MD_PRICE_LIST` 的关联查询，如果砂仁在价格表中没有有效记录，可能导致查询结果为空。

### 2. 参数匹配问题
- DRUG_SPEC 与 PACKAGE_SPEC 的匹配
- FIRM_ID 的匹配（重庆康嘉 vs 四川莉君）
- 时间条件的匹配

### 3. 数据一致性问题
- DRUG_DICT 表中的记录与 DRUG_STOCK 表不匹配
- 规格字段的数据格式不一致

## 解决方案

### 立即解决方案
1. **添加详细的调试日志**，记录GetDrugStockForUpdate方法的查询参数和结果
2. **临时绕过价格表关联**，先确保基本的库存查询能正常工作

### 根本解决方案
1. **统一库存验证逻辑**，只使用一套验证机制
2. **修复数据一致性问题**
3. **优化SQL查询条件**

## 下一步行动
1. 先添加调试日志，确定具体的失败原因
2. 根据日志结果制定针对性的修复方案
