[2025-08-28 19:12:02.345] [ORDER_SYNC] [2025-8-22] ORDER_NO同步检查 - 内存最大: 2, 数据库最大: 2, 分配: 3
[2025-08-28 19:12:02.346] [MAXNO] [OrderBusiness.MaxOrderNo.set] 属性直接设置 - MaxOrderNo变化: 2 -> 3
[2025-08-28 19:12:02.362] [MAXNO] [OrderBusiness.Add] MaxOrderNo递增（内存中医嘱数量: 1） - MaxOrderNo变化: 2 -> 3
[2025-08-28 19:12:02.363] [ORDER] [OrderBusiness.Add] 新增医嘱 - CLINIC_NO: 250717000ADMIN00001, ORDER_NO: 3, ORDER_SUB_NO: 1, ITEM_NO: 1, ORDER_TEXT: , ORDER_CLASS: 
[2025-08-28 19:12:06.502] [CHECKPOINT] [UcDrugPresc.Save] 检查点[开始保存] - 西药保存按钮被点击
[2025-08-28 19:12:06.503] [CHECKPOINT] [UcDrugPresc.Save] 待保存医嘱 - 医嘱数量: 0
[2025-08-28 19:12:06.515] [CHECKPOINT] [UcDrugPresc.Save] 检查点[无医嘱保存] - ordersSave为空或数量为0
[2025-08-28 19:12:06.530] [CHECKPOINT] [UcCdrugPresc.Save] 开始保存 - 中药保存按钮被点击
[2025-08-28 19:12:06.531] [BATCH] [UcCdrugPresc.Save] 待保存医嘱 - 医嘱数量: 1
[2025-08-28 19:12:06.531] [BATCH] [UcCdrugPresc.Save] 待保存医嘱[1] - CLINIC_NO: 250717000ADMIN00001, ORDER_NO: 3, ORDER_SUB_NO: 1, ITEM_NO: 1, ORDER_TEXT: 砂仁, ORDER_CLASS: B, STATE: 录入
[2025-08-28 19:12:06.535] [BATCH] [OrderBusiness.Save] 保存前状态 - 医嘱数量: 1
[2025-08-28 19:12:06.535] [BATCH] [OrderBusiness.Save] 保存前状态[1] - CLINIC_NO: 250717000ADMIN00001, ORDER_NO: 3, ORDER_SUB_NO: 1, ITEM_NO: 1, ORDER_TEXT: 砂仁, ORDER_CLASS: B, STATE: 录入
[2025-08-28 19:12:06.536] [CHECKPOINT] [OrderBusiness.Save] 检查点[开始保存] - 医嘱数量: 1, MaxOrderNo: 3
[2025-08-28 19:12:06.676] [CHECKPOINT] [OrderBusiness.Save] 检查点[中药库存检查] - 中药数量: 1
[2025-08-28 19:12:07.100] [CHECKPOINT] [OrderBusiness.Save] 检查点[保存时ITEM_NO] - ORDER_NO: 4, ORDER_SUB_NO: 1, ITEM_NO: 1, ORDER_TEXT: 砂仁
[2025-08-28 19:12:07.122] [CHECKPOINT] [OrderBusiness.Save] 检查点[费用记录处理] - ORDER_TEXT: 砂仁, ORDER_NO: 4, ORDER_SUB_NO: 1, ITEM_NO: 1, STATE: 保存
[2025-08-28 19:12:07.126] [CHECKPOINT] [OrderBusiness.Save] 检查点[数据库执行前] - SQL语句数量: 3
[2025-08-28 19:12:07.162] [CHECKPOINT] [OrderBusiness.Save] 检查点[数据库执行] - 执行结果: True
[2025-08-28 19:12:07.663] [CHECKPOINT] [OrderBusiness.Save] 检查点[保存成功] - 已重置缓存，MaxOrderNo: 3
[2025-08-28 19:12:07.663] [CHECKPOINT] [OrderBusiness.Save] 数据库执行 - 执行结果: True
[2025-08-28 19:12:07.724] [MAXNO] [OrderBusiness.MaxOrderNo.set] 属性直接设置 - MaxOrderNo变化: 3 -> 4
[2025-08-28 19:12:07.724] [MAXNO] [OrderBusiness.SetMaxOrderNo] 根据现有医嘱设置（医嘱数量: 2） - MaxOrderNo变化: 3 -> 4
[2025-08-28 19:12:08.341] [MAXNO] [OrderBusiness.SetMaxOrderNo] 根据现有医嘱设置（医嘱数量: 2） - MaxOrderNo变化: 4 -> 4
[2025-08-28 19:12:08.734] [CHECKPOINT] [UcCdrugPresc.Save] 保存成功 - 中药保存成功
[2025-08-28 19:12:08.949] [MAXNO] [OrderBusiness.SetMaxOrderNo] 根据现有医嘱设置（医嘱数量: 2） - MaxOrderNo变化: 4 -> 4
[2025-08-28 19:12:17.588] [ORDER_SYNC] [2025-8-22] ORDER_NO同步检查 - 内存最大: 4, 数据库最大: 4, 分配: 5
[2025-08-28 19:12:17.589] [MAXNO] [OrderBusiness.MaxOrderNo.set] 属性直接设置 - MaxOrderNo变化: 4 -> 5
[2025-08-28 19:12:17.605] [MAXNO] [OrderBusiness.Add] MaxOrderNo递增（内存中医嘱数量: 2） - MaxOrderNo变化: 4 -> 5
[2025-08-28 19:12:17.605] [ORDER] [OrderBusiness.Add] 新增医嘱 - CLINIC_NO: 250717000ADMIN00001, ORDER_NO: 5, ORDER_SUB_NO: 1, ITEM_NO: 1, ORDER_TEXT: , ORDER_CLASS: 
[2025-08-28 19:12:25.138] [CHECKPOINT] [UcDrugPresc.Save] 检查点[开始保存] - 西药保存按钮被点击
[2025-08-28 19:12:25.139] [CHECKPOINT] [UcDrugPresc.Save] 待保存医嘱 - 医嘱数量: 0
[2025-08-28 19:12:25.139] [CHECKPOINT] [UcDrugPresc.Save] 检查点[无医嘱保存] - ordersSave为空或数量为0
[2025-08-28 19:12:25.142] [CHECKPOINT] [UcCdrugPresc.Save] 开始保存 - 中药保存按钮被点击
[2025-08-28 19:12:25.142] [BATCH] [UcCdrugPresc.Save] 待保存医嘱 - 医嘱数量: 1
[2025-08-28 19:12:25.143] [BATCH] [UcCdrugPresc.Save] 待保存医嘱[1] - CLINIC_NO: 250717000ADMIN00001, ORDER_NO: 5, ORDER_SUB_NO: 1, ITEM_NO: 1, ORDER_TEXT: 砂仁, ORDER_CLASS: B, STATE: 录入
[2025-08-28 19:12:25.143] [BATCH] [OrderBusiness.Save] 保存前状态 - 医嘱数量: 1
[2025-08-28 19:12:25.143] [BATCH] [OrderBusiness.Save] 保存前状态[1] - CLINIC_NO: 250717000ADMIN00001, ORDER_NO: 5, ORDER_SUB_NO: 1, ITEM_NO: 1, ORDER_TEXT: 砂仁, ORDER_CLASS: B, STATE: 录入
[2025-08-28 19:12:25.143] [CHECKPOINT] [OrderBusiness.Save] 检查点[开始保存] - 医嘱数量: 1, MaxOrderNo: 5
[2025-08-28 19:12:25.212] [CHECKPOINT] [OrderBusiness.Save] 检查点[中药库存检查] - 中药数量: 1
[2025-08-28 19:12:25.516] [CHECKPOINT] [OrderBusiness.Save] 检查点[保存时ITEM_NO] - ORDER_NO: 6, ORDER_SUB_NO: 1, ITEM_NO: 1, ORDER_TEXT: 砂仁
[2025-08-28 19:12:25.533] [CHECKPOINT] [OrderBusiness.Save] 检查点[费用记录处理] - ORDER_TEXT: 砂仁, ORDER_NO: 6, ORDER_SUB_NO: 1, ITEM_NO: 1, STATE: 保存
[2025-08-28 19:12:25.533] [CHECKPOINT] [OrderBusiness.Save] 检查点[数据库执行前] - SQL语句数量: 3
[2025-08-28 19:12:25.565] [CHECKPOINT] [OrderBusiness.Save] 检查点[数据库执行] - 执行结果: True
[2025-08-28 19:12:25.819] [CHECKPOINT] [OrderBusiness.Save] 检查点[保存成功] - 已重置缓存，MaxOrderNo: 5
[2025-08-28 19:12:25.819] [CHECKPOINT] [OrderBusiness.Save] 数据库执行 - 执行结果: True
[2025-08-28 19:12:25.896] [MAXNO] [OrderBusiness.MaxOrderNo.set] 属性直接设置 - MaxOrderNo变化: 5 -> 6
[2025-08-28 19:12:25.897] [MAXNO] [OrderBusiness.SetMaxOrderNo] 根据现有医嘱设置（医嘱数量: 3） - MaxOrderNo变化: 5 -> 6
[2025-08-28 19:12:26.400] [MAXNO] [OrderBusiness.SetMaxOrderNo] 根据现有医嘱设置（医嘱数量: 3） - MaxOrderNo变化: 6 -> 6
