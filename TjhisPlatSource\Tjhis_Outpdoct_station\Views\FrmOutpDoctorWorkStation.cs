﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;
using Tjhis.Outpdoct.Station.Common;

namespace Tjhis.Outpdoct.Station.Views
{
    public partial class FrmOutpDoctorWorkStation : Form
    {
        public FrmOutpDoctorWorkStation()
        {
            InitializeComponent();

            // 程序启动时强制刷新参数缓存
            try
            {
                PrescParameter.RefreshCheckModel();

                string logPath = @"..\Client\LOG\exLOG\门诊医生站_启动日志_" + DateTime.Now.ToString("yyyyMMdd") + ".log";
                string logEntry = string.Format(
                    "[{0:yyyy-MM-dd HH:mm:ss}] [INFO] [程序启动] 门诊医生站启动，已刷新参数缓存\r\n",
                    DateTime.Now
                );
                System.IO.File.AppendAllText(logPath, logEntry);
            }
            catch (Exception ex)
            {
                // 记录启动异常日志
                try
                {
                    string logPath = @"..\Client\LOG\exLOG\门诊医生站_启动异常_" + DateTime.Now.ToString("yyyyMMdd") + ".log";
                    string logEntry = string.Format(
                        "[{0:yyyy-MM-dd HH:mm:ss}] [ERROR] [程序启动] 启动时刷新参数失败: {1}\r\n",
                        DateTime.Now, ex.Message
                    );
                    System.IO.File.AppendAllText(logPath, logEntry);
                }
                catch { /* 忽略日志错误 */ }
            }
        }
    }
}
