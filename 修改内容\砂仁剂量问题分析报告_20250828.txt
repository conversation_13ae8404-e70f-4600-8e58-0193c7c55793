================================================================================
                    砂仁剂量问题分析报告
================================================================================
创建时间：2025-08-28
问题描述：点击新方后砂仁显示5g但实际保存为1g的问题分析
状态：问题已定位，待明日继续处理

================================================================================
                        问题现象
================================================================================

1. 用户操作流程：
   - 点击"新方"按钮
   - 输入砂仁药品
   - 界面显示剂量为5g
   - 但实际保存的数据为1g

2. 日志证据：
   - 界面事件日志：字段=DOSAGE, 药品=砂仁, 修改前剂量=5g, 新值=5
   - 剂量追踪日志：进入时dosage=1.0g, 处方列表dosage=g（空值）

================================================================================
                        问题根本原因
================================================================================

1. 界面显示与实际数据不一致：
   - 界面显示：5g（来源未明确）
   - 实际数据：1g（来自drugDict.DOSE_PER_UNIT）

2. 处方列表DOSAGE字段为空：
   - 这是导致SetAmout计算错误的根本原因
   - prescListOrder.DOSAGE = 空值，导致剂量计算失败

3. 多个调用点存在问题：
   - layoutView1_CellValueChanged (第1448行)
   - SetCDrugProperty方法调用
   - SetAmout方法的参数传递

================================================================================
                        代码分析结果
================================================================================

1. 关键调用链：
   AddDrug -> AddDrugBase -> SetClinciItem -> SetCDrugProperty -> SetAmout

2. DOSAGE字段设置位置：
   - AddDrugBase第572行：order.DOSAGE = dosage;
   - 第440行：decimal dosage = result.LabAddFromType == Enums.AddFromType.Input ? 
             drugDict.DOSE_PER_UNIT.ToDecimal(0) : result.Dose_per_unit.ToDecimal(0);

3. 问题调用点：
   a) UcCdrugPresc.cs第1448行：
      this.PrescBusiness.SetCDrugProperty(this.GetEditModel<OUTP_ORDERS_STANDARD>(this.bsPrescList), order);
      
   b) UcCdrugPresc.cs第962行：
      this.PrescBusiness.SetCDrugProperty(this.GetEditModel<OUTP_ORDERS_STANDARD>(this.bsPrescList), order);

4. SetAmout方法调用点：
   - CDrugPresc.cs第886行
   - CDrugPresc.cs第1991行

================================================================================
                        已尝试的修复方案
================================================================================

1. 修复SetAmout方法：
   - 添加了详细的调试日志
   - 修复了AMOUNT计算逻辑
   - 状态：部分有效，但未解决根本问题

2. 修复layoutView1_CellValueChanged：
   - 确保prescListOrder.DOSAGE有正确的值
   - 添加调试日志追踪
   - 状态：已回滚，需要重新实施

3. 添加调试日志：
   - 剂量追踪日志
   - 界面事件日志
   - AddDrugBase剂量设置日志
   - 状态：已回滚，需要重新添加

================================================================================
                        待解决的关键问题
================================================================================

1. 界面显示5g的真正来源：
   - 需要确定是来自哪个数据源
   - 可能是界面控件的默认值
   - 可能是数据绑定的缓存问题

2. 处方列表DOSAGE为空的原因：
   - bsPrescList中的DOSAGE字段未正确设置
   - 需要在NewPresc或相关方法中设置默认值

3. 数据同步问题：
   - 界面修改后需要同步到处方列表
   - SetCDrugProperty调用时参数不正确

================================================================================
                        明日处理方案
================================================================================

1. 第一步：重新添加调试日志
   - 在AddDrugBase中添加剂量设置日志
   - 在layoutView1_CellValueChanged中添加修复逻辑
   - 在SetAmout中保持现有日志

2. 第二步：数据库配置检查
   - 执行砂仁剂量配置检查.sql
   - 确认DRUG_RATIONAL_DOSAGE表中砂仁的NORMAL_DOSAGE值
   - 确认DRUG_DICT表中砂仁的DOSE_PER_UNIT值

3. 第三步：界面数据绑定修复
   - 确保NewPresc方法设置正确的DOSAGE默认值
   - 修复SetCDrugProperty的参数传递
   - 确保界面显示与实际数据一致

4. 第四步：测试验证
   - 点击新方测试
   - 输入砂仁并修改剂量
   - 验证保存后的数据正确性

================================================================================
                        技术要点记录
================================================================================

1. 关键文件：
   - TjhisPlatSource/Tjhis_Outpdoct_station/Views/Presc/UcCdrugPresc.cs
   - TjhisPlatSource/Tjhis_Outpdoct_station/Business/CDrugPresc.cs
   - TjhisPlatSource/Tjhis_Outpdoct_station/Business/PrescBusiness.cs

2. 关键方法：
   - SetAmout：剂量计算核心方法
   - SetCDrugProperty：属性设置方法
   - AddDrugBase：药品添加基础方法
   - layoutView1_CellValueChanged：界面事件处理

3. 关键数据表：
   - DRUG_DICT：药品基础信息（DOSE_PER_UNIT）
   - DRUG_RATIONAL_DOSAGE：药品常用剂量（NORMAL_DOSAGE）
   - OUTP_ORDERS_STANDARD：处方医嘱表

4. 日志文件位置：
   - ..\Client\LOG\exLOG\门诊医生站_剂量追踪_YYYYMMDD.log
   - ..\Client\LOG\exLOG\门诊医生站_界面事件_YYYYMMDD.log

================================================================================
                        注意事项
================================================================================

1. 编译环境：Visual Studio 2017，不要尝试编译代码
2. 数据库：Oracle
3. UI控件：DevExpress 19.1
4. 操作系统：Windows 10 专业版

5. 安全原则：
   - 禁止覆盖现有逻辑
   - 保持向后兼容性
   - 最小化风险

================================================================================
                        问题状态
================================================================================

当前状态：问题已定位但未完全解决
下一步：明日继续处理，重新实施修复方案
优先级：高（影响用户正常使用）

================================================================================
