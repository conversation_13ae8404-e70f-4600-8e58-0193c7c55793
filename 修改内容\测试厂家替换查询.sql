-- 测试查询：验证砂仁药品的库存情况
-- 用于验证厂家替换功能是否能正常工作

-- 1. 查询砂仁在110202药房的所有库存（不限制厂家）
SELECT STORAGE, DRUG_CODE, DRUG_NAME, DRUG_SPEC, PACKAGE_SPEC, 
       FIRM_ID, QUANTITY, SUPPLY_INDICATOR, BATCH_NO
FROM DRUG_STOCK 
WHERE DRUG_CODE = '63080238YP1' 
  AND STORAGE = '110202'
  AND DRUG_SPEC = '1g'
  AND PACKAGE_SPEC = '1g'
  AND SUPPLY_INDICATOR = '1'
  AND HIS_UNIT_CODE = '45038900950011711A6001'
  AND QUANTITY > 0
ORDER BY EXPIRE_DATE ASC, BATCH_NO ASC;

-- 2. 查询特定厂家（重庆康嘉）的库存
SELECT STORAGE, DRUG_CODE, DRUG_NAME, FIRM_ID, QUANTITY
FROM DRUG_STOCK 
WHERE DRUG_CODE = '63080238YP1' 
  AND STORAGE = '110202'
  AND FIRM_ID = '重庆康嘉'
  AND SUPPLY_INDICATOR = '1'
  AND HIS_UNIT_CODE = '45038900950011711A6001'
  AND QUANTITY > 0;

-- 3. 查询特定厂家（四川莉君）的库存
SELECT STORAGE, DRUG_CODE, DRUG_NAME, FIRM_ID, QUANTITY
FROM DRUG_STOCK 
WHERE DRUG_CODE = '63080238YP1' 
  AND STORAGE = '110202'
  AND FIRM_ID = '四川莉君'
  AND SUPPLY_INDICATOR = '1'
  AND HIS_UNIT_CODE = '45038900950011711A6001'
  AND QUANTITY > 0;

-- 4. 汇总查询：按厂家分组显示库存
SELECT FIRM_ID, SUM(QUANTITY) as TOTAL_QUANTITY, COUNT(*) as BATCH_COUNT
FROM DRUG_STOCK 
WHERE DRUG_CODE = '63080238YP1' 
  AND STORAGE = '110202'
  AND SUPPLY_INDICATOR = '1'
  AND HIS_UNIT_CODE = '45038900950011711A6001'
GROUP BY FIRM_ID
ORDER BY TOTAL_QUANTITY DESC;