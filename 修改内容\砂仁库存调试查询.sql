-- 调试查询：检查砂仁药品在处方中的情况
-- 执行时间：2025-08-28

-- 1. 查看砂仁的所有库存
SELECT 
    STORAGE as 药房,
    DRUG_CODE as 药品代码, 
    DRUG_NAME as 药品名称,
    DRUG_SPEC as 药品规格,
    PACKAGE_SPEC as 包装规格,
    FIRM_ID as 厂家,
    BATCH_NO as 批次号,
    QUANTITY as 库存数量,
    SUPPLY_INDICATOR as 可供标志
FROM DRUG_STOCK 
WHERE DRUG_CODE = '63080238YP1'
  AND HIS_UNIT_CODE = '45038900950011711A6001'
ORDER BY STORAGE, FIRM_ID;

-- 2. 查看今天的处方中是否有砂仁
SELECT 
    p.PRESC_NO as 处方号,
    p.PRESC_DATE as 处方日期,
    p.DISPENSARY as 药房,
    d.DRUG_CODE as 药品代码,
    d.DRUG_NAME as 药品名称,
    d.DRUG_SPEC as 药品规格,
    d.PACKAGE_SPEC as 包装规格,
    d.FIRM_ID as 厂家,
    d.QUANTITY as 数量,
    d.PACKAGE_UNITS as 单位
FROM DRUG_PRESC_MASTER p
INNER JOIN DRUG_PRESC_DETAIL d ON p.PRESC_NO = d.PRESC_NO
WHERE d.DRUG_CODE = '63080238YP1'
  AND p.PRESC_DATE >= TRUNC(SYSDATE)
  AND p.HIS_UNIT_CODE = '45038900950011711A6001'
ORDER BY p.PRESC_DATE DESC;

-- 3. 检查是否有不同厂家的砂仁库存
SELECT 
    FIRM_ID as 厂家,
    COUNT(*) as 批次数,
    SUM(QUANTITY) as 总库存
FROM DRUG_STOCK 
WHERE DRUG_CODE = '63080238YP1'
  AND STORAGE = '110202'
  AND HIS_UNIT_CODE = '45038900950011711A6001'
GROUP BY FIRM_ID;

-- 4. 查看最近的发药记录（检查是否有砂仁）
SELECT 
    PRESC_NO as 处方号,
    DRUG_CODE as 药品代码,
    DRUG_NAME as 药品名称,
    FIRM_ID as 厂家,
    QUANTITY as 数量,
    DISPENSE_DATE as 发药时间
FROM DRUG_PRESC_DETAIL_TEMP
WHERE DRUG_CODE = '63080238YP1'
  AND DISPENSE_DATE >= SYSDATE - 1
ORDER BY DISPENSE_DATE DESC;