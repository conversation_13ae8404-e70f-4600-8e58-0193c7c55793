<?xml version="1.0" encoding="utf-8"?>
<configuration>
  <configSections>
    <sectionGroup name="applicationSettings" type="System.Configuration.ApplicationSettingsGroup, System, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089">
      <section name="NursingPlatform.Properties.Settings" type="System.Configuration.ClientSettingsSection, System, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" requirePermission="false" />
    </sectionGroup>
  </configSections>
  <applicationSettings>
    <NursingPlatform.Properties.Settings>
      <setting name="NursingPlatform_TGWebReference_webService" serializeAs="String">
        <value>http://61.161.147.108:6002/webService.asmx</value>
      </setting>
    </NursingPlatform.Properties.Settings>
  </applicationSettings>
  <appSettings>
    <add key="SEATCODE" value="1" />
    <add key="LastLoginUser" value="ADMIN" />
    <add key="IfUseMengMa" value="1" />
    <add key="MsgCenterMode" value="1" />
    <add key="ClientSettingsProvider.ServiceUri" value="" />
    <add key="LIS_PRINT_URL" value="http://192.168.111.46:7001/" />
    <add key="PACS_PRINT_URL" value="http://192.168.111.46:7001/" />
    <add key="huayandayin" value="Zebra GK888t - ZPL" />
    <add key="shuyedayin" value="Zebra GK888t - ZPL" />
    <add key="AFTERNOON_DISPTIME" value="11:00:00" />
    <add key="AFTERNOON_STARTTIME" value="12:00:00" />
    <add key="REG_MODE" value="0" />
    <add key="COLORFLAG" value="0" />
    <add key="AUTO_CLINIC" value="NO" />
    <add key="SMALL_PRINTER" value="0" />
    <add key="WINDOW_NO" value="0" />
    <add key="UNITCODE" value="45038900950011711A6001" />
    <add key="REGISTER_PRINT" value="导出为WPS PDF" />
    <add key="OBILLING_CHARGE_PRINT" value="HPABB1AE (HP LaserJet Pro MFP 3101-3108)" />
    <add key="MASTER_PRINT" value="\\172.19.15.200\outprcpt" />
    <add key="ITEM_PRINT" value="FinePrint" />
    <add key="PRINT_DETAIL_FLAG" value="1" />
    <add key="MZ_LXWIN" value="1" />
    <add key="BARCODE_PRINT" value="\\172.19.15.200\barcode" />
    <add key="SENDSERVER" value="192.9.201.40" />
    <add key="DRUG_WINDOWS_NO" value="1" />
    <add key="Server" value="192.9.200.81" />
    <add key="Port" value="9000" />
    <add key="BILL_FLAG" value="1" />
    <add key="CNSLT_FLAG" value="1" />
    <add key="COUNSEL_NO" value="5" />
    <add key="ROOM_CODE" value="17010100" />
    <add key="EXAM_GROUP_CODE" value="" />
    <add key="PACS_FLAG" value="0" />
    <add key="TJ_RCPT" value="YES" />
    <add key="HXDZCOMM" value="4" />
    <add key="SUPPLY_DEPT" value="190101" />
    <add key="DEPT_LEVEL" value="" />
    <add key="YL1" value="YES" />
    <add key="YL2" value="YES" />
    <add key="YL3" value="YES" />
    <add key="YL4" value="YES" />
    <add key="YL5" value="YES" />
    <add key="A4" value="FinePrint" />
    <add key="A5" value="FinePrint" />
    <add key="B5" value="FinePrint" />
    <add key="B6" value="FinePrint" />
    <add key="Letter" value="FinePrint" />
    <add key="skinName" value="" />
  </appSettings>
  <system.serviceModel>
    <bindings>
      <basicHttpBinding>
        <binding name="HosPlatWSSoap" maxBufferPoolSize="**********"
          maxReceivedMessageSize="**********" />
        <binding name="webServiceSoap" maxBufferPoolSize="**********"
          maxBufferSize="**********" maxReceivedMessageSize="**********" />
      </basicHttpBinding>
      <netTcpBinding>
        <binding name="netTcpBinding" receiveTimeout="23:59:59" maxBufferPoolSize="**********"
          maxReceivedMessageSize="**********">
          <reliableSession ordered="true" inactivityTimeout="23:59:59"
            enabled="true" />
          <security mode="None" />
        </binding>
      </netTcpBinding>
      <wsHttpBinding>
      </wsHttpBinding>
    </bindings>
  </system.serviceModel>
  <system.web>
    <membership defaultProvider="ClientAuthenticationMembershipProvider">
      <providers>
        <add name="ClientAuthenticationMembershipProvider" type="System.Web.ClientServices.Providers.ClientFormsAuthenticationMembershipProvider, System.Web.Extensions, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35" serviceUri="" />
      </providers>
    </membership>
    <roleManager enabled="true" defaultProvider="ClientRoleProvider">
      <providers>
        <add name="ClientRoleProvider" type="System.Web.ClientServices.Providers.ClientRoleProvider, System.Web.Extensions, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35" serviceUri="" cacheTimeout="86400" />
      </providers>
    </roleManager>
  </system.web>
  <startup useLegacyV2RuntimeActivationPolicy="true">
    <supportedRuntime version="v4.0" sku=".NETFramework,Version=v4.5" />
    <supportedRuntime version="v2.0.50727" />
  </startup>
</configuration>