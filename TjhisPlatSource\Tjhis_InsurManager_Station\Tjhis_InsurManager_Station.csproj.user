﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="15.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
    <ProjectView>ShowAllFiles</ProjectView>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'Debug|AnyCPU'">
    <StartAction>Program</StartAction>
    <StartProgram>D:\Code\TjhisPlatSource\TJHisPlatEXE\Client\TJHisPlat.exe</StartProgram>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'Debug|x86'">
    <StartAction>Program</StartAction>
    <StartProgram>..\TjhisPlatEXE\TJHisPlat.exe</StartProgram>
    <StartWorkingDirectory>..\TjhisPlatEXE\</StartWorkingDirectory>
  </PropertyGroup>
</Project>